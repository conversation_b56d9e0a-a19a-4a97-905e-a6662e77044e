# Liquid Glass 效果测试指南

## 测试环境要求
- iOS 18.0+ 设备或模拟器
- Xcode 16+ 
- 支持iOS 26 SDK

## 测试步骤

### 1. 基本功能测试
1. 启动应用并导航到ScrollViewController
2. 验证工具栏是否显示Liquid Glass效果
3. 检查工具栏是否有正确的圆角和阴影
4. 确认按钮是否正确显示在vibrancy容器中

### 2. 视觉效果测试
1. **玻璃质感**:
   - 工具栏应该有半透明的玻璃效果
   - 背景内容应该透过工具栏可见但有模糊效果
   
2. **按钮效果**:
   - 按钮应该有微妙的边框
   - 按钮圆角应该是18pt
   - 按钮应该有内阴影效果

3. **阴影效果**:
   - 工具栏下方应该有柔和的阴影
   - 阴影应该有20pt的模糊半径
   - 阴影偏移应该是(0, 8)

### 3. 交互测试
1. **按钮触摸**:
   - 按下按钮时应该有缩放动画(0.92倍)
   - 按钮透明度应该变为0.75
   - 边框颜色应该变亮
   - 应该有触觉反馈

2. **按钮释放**:
   - 应该有弹簧回弹动画
   - 按钮应该恢复原始大小和透明度
   - 边框颜色应该恢复原始状态

### 4. 布局测试
1. **设备旋转**:
   - 旋转设备时工具栏应该正确调整大小
   - 阴影路径应该正确更新
   - 按钮布局应该保持正确

2. **不同屏幕尺寸**:
   - 在不同设备上测试工具栏显示
   - 确认按钮间距和大小适当

### 5. 兼容性测试
1. **iOS 18+设备**:
   - 应该显示完整的Liquid Glass效果
   
2. **iOS 17及以下设备**:
   - 应该回退到传统的UIBlurEffect
   - 功能应该正常工作，只是视觉效果不同

## 预期结果

### iOS 18+ 设备
- 工具栏具有现代的Liquid Glass外观
- 按钮有精致的玻璃质感
- 交互动画流畅自然
- 触觉反馈响应及时

### iOS 17及以下设备
- 工具栏使用传统模糊效果
- 基本功能正常工作
- 按钮交互正常

## 常见问题排查

### 问题1: 工具栏没有玻璃效果
- 检查iOS版本是否为18.0+
- 确认UIGlassEffect API是否可用
- 检查是否正确设置了effect属性

### 问题2: 按钮显示异常
- 检查vibrancy容器是否正确创建
- 确认按钮是否正确添加到contentView
- 验证约束是否正确设置

### 问题3: 触摸动画不流畅
- 检查动画参数设置
- 确认弹簧动画的damping和velocity值
- 验证触摸事件是否正确绑定

### 问题4: 阴影效果不正确
- 检查shadowPath是否正确设置
- 确认masksToBounds设置为NO
- 验证阴影参数值

## 性能注意事项
- Liquid Glass效果可能比传统模糊效果消耗更多GPU资源
- 在低端设备上注意性能表现
- 监控电池使用情况

## 反馈收集
测试时请注意记录：
1. 视觉效果是否符合预期
2. 交互体验是否流畅
3. 性能表现如何
4. 是否有任何异常或崩溃
