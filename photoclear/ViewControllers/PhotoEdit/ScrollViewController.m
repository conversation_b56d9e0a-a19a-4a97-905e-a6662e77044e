//
//  ScrollViewController.m
//  photoclear
//
//  Created by lifubing on 2021/6/12.
//

#import "ScrollViewController.h"

#import "TimeHelper.h"
#import "UIView+Extension.h"
#import "FQ_CollectionViewCell.h"
#import "Preferences.h"
#import "UIButton+CenterImageAndTitle.h"
#import "AlbumSelectViewController.h"
#import "UICollectionViewLineFlowLayout.h"
#import "DeleteListViewController.h"
#import "PhotoTools.h"
#import "PhotoSettingsViewController.h"

@interface ScrollViewController () <UICollectionViewDelegate, UICollectionViewDataSource, AlbumSelectViewControllerDelegate, FQ_CollectionViewCellDelegate, DeleteListViewControllerDelegate, PhotoSettingsViewControllerDelegate>
//@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UICollectionView *albumCollectionView;
@property (weak, nonatomic) IBOutlet UIVisualEffectView *albumBackView;

@property (weak, nonatomic) IBOutlet UIButton *addAlbumButton;


@property (weak, nonatomic) IBOutlet UILabel *infoLabel;
@property (weak, nonatomic) IBOutlet UIButton *deleteBtn;
@property (weak, nonatomic) IBOutlet UIButton *recycleBtn;
@property (weak, nonatomic) IBOutlet UILabel *recycleInfoLabel;
@property (weak, nonatomic) IBOutlet UIButton *titleBtn;

// 工具栏相关
@property (weak, nonatomic) IBOutlet UIVisualEffectView *toolbarBlurView;
@property (weak, nonatomic) IBOutlet UIView *toolbarView;
@property (weak, nonatomic) IBOutlet UIButton *archiveBtn;
@property (weak, nonatomic) IBOutlet UIButton *nextBtn;

@property (strong, nonatomic) UIVisualEffectView *vibrancyEffectView;

@property (nonatomic, strong) NSIndexPath *currentIndexPath;

@property (nonatomic, strong) UICollectionView *collectionView;;

@property (nonatomic, strong) PHImageRequestOptions *requestOptions;

@property (nonatomic, strong) PHFetchResult<PHAssetCollection *> *smartAlbumsArray;


@property (nonatomic, strong) NSMutableArray <NSString *> *deletePhArray;
@property (nonatomic, strong) NSMutableArray <NSString *> *archivePhArray;


@property (nonatomic, strong) UIImage *willDisplayCellImage;


@property (nonatomic, assign) BOOL isShowAlbumsTableView;
@property (nonatomic, assign) CGSize imageTargetSize;

@property (nonatomic, assign) CGFloat cellwidth;
@property (nonatomic, assign) CGFloat cellMargin;

@property (nonatomic, assign) NSInteger    currentPage;

@property (nonatomic, strong) NSTimer *scrollTimer;
@end

@implementation ScrollViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // 初始化collectionView（这会根据设置配置滚动方向）
    [self collectionView];
    
    if (@available(iOS 11.0, *)) {
        self.collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    self.imageTargetSize = [self getTargetSize];
    
    // 监听来自小组件的照片选择通知
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(handleShowPhotoFromWidget:) 
                                                 name:@"ShowPhotoFromWidget" 
                                               object:nil];
    
    [self.titleBtn setTitle:@"最近项目" forState:UIControlStateNormal];
    [self.titleBtn setImage:[UIImage imageNamed:@"change"] forState:UIControlStateNormal];
    [self.titleBtn sizeToFit];
    [self.titleBtn horizontalCenterTitleAndImage:0];
    
    
    [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
        if (status == PHAuthorizationStatusAuthorized) {
          //code
        }
    }];
    
    self.requestOptions = [[PHImageRequestOptions alloc] init];
    self.requestOptions.resizeMode   = PHImageRequestOptionsResizeModeExact;
    self.requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
    
    
    self.cellMargin = 8;
    self.cellwidth = self.view.frame.size.width;
//    floorf((self.view.frame.size.width - self.cellMargin * 4) / 3);
    
//    FQ_CollectionViewFlowLayout * flowLayout = [[FQ_CollectionViewFlowLayout alloc]init];
//    flowLayout.minimumLineSpacing = BUBBLE_PADDING;
//    _collectionView.collectionViewLayout = flowLayout;
    
    // 1.创建流水布局
//    UICollectionViewLineFlowLayout *layout = [[UICollectionViewLineFlowLayout alloc] init];
    
//    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
//    layout.minimumLineSpacing = 0.0;
//    layout.minimumInteritemSpacing = 0.0;
//    layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    
//    self.collectionView.collectionViewLayout = layout;
    
//    [_collectionView registerClass:[FQ_CollectionViewCell class] forCellWithReuseIdentifier:@"FQ_CollectionViewCellID"];
//    _collectionView.delegate = self;
//    _collectionView.dataSource = self;

//    _collectionView.pagingEnabled = YES;
//    _collectionView.showsVerticalScrollIndicator = NO;
//    _collectionView.showsHorizontalScrollIndicator = NO;
    
    
    {
        // 所有智能相册
        PHFetchResult *smartAlbums = [PHAssetCollection fetchAssetCollectionsWithType:PHAssetCollectionTypeAlbum subtype:PHAssetCollectionSubtypeAny options:nil];
        self.smartAlbumsArray = smartAlbums;
        
        self.albumBackView.layer.cornerRadius = 8;
        self.albumBackView.layer.masksToBounds = YES;
//        ScrollActionCollectionViewCellFlowLayout *albumflowLayout = [[ScrollActionCollectionViewCellFlowLayout alloc]init];
//        albumflowLayout.minimumLineSpacing = 0;
//        self.albumCollectionView.collectionViewLayout = albumflowLayout;
        
//        [self.albumCollectionView registerClass:[ScrollActionCollectionViewCell class] forCellWithReuseIdentifier:@"ScrollActionCollectionViewCell"];
        [self.albumCollectionView registerNib:[UINib nibWithNibName:@"ScrollActionCollectionViewCell" bundle:nil] forCellWithReuseIdentifier:@"ScrollActionCollectionViewCell"];
        self.albumCollectionView.delegate = self;
        self.albumCollectionView.dataSource = self;

        self.albumCollectionView.pagingEnabled = YES;
        self.albumCollectionView.showsVerticalScrollIndicator = NO;
        self.albumCollectionView.showsHorizontalScrollIndicator = NO;
    }
    
    [self.recycleBtn sizeToFit];
    self.recycleBtn.width += 22;
    self.recycleBtn.height += 12;
    self.recycleBtn.layer.cornerRadius = 4;

//    [self reloadCollectionView];
    
    [self.addAlbumButton sizeToFit];
    if (self.addAlbumButton.imageView.image) {
        [self setCompleteButton:self.addAlbumButton iconString:@"新增相簿" image:self.addAlbumButton.imageView.image];
    }
    
    
//    [self collectionView];
    
    dispatch_async (dispatch_get_main_queue (), ^{
        if (self.startIndexPath) {
            self.currentIndexPath = self.startIndexPath;
            [self.collectionView scrollToItemAtIndexPath:self.startIndexPath atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
        }
    });
    
    // 添加长按手势来标记/取消标记照片
    UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(handleLongPress:)];
    longPress.minimumPressDuration = 0.5;
    [self.view addGestureRecognizer:longPress];
    
    // 添加滑动手势
    [self setupSwipeGestures];
    
    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(singleTapd:)];
    [self.view addGestureRecognizer:tapGesture];

    // 设置工具栏按钮的图标和文字布局
    [self setupToolbarButtons];

    // 设置工具栏玻璃效果的圆角
    [self setupToolbarBlurEffect];
    
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.deletePhArray = [[NSMutableArray alloc] initWithArray:[Preferences sharedInstance].deleteArray];
    if (!self.deletePhArray) {
        self.deletePhArray = [NSMutableArray new];
    }

    self.archivePhArray = [[NSMutableArray alloc] initWithArray:[Preferences sharedInstance].archiveArray];
    if (!self.archivePhArray) {
        self.archivePhArray = [NSMutableArray new];
    }
    self.recycleInfoLabel.text = [NSString stringWithFormat:@"删除(%ld)", self.deletePhArray.count];
    [self.albumCollectionView reloadData];
    
    // 检查是否有来自小组件的照片选择请求
    [self checkForWidgetPhotoSelection];
    
    // 根据设置启动自动播放
    if ([Preferences sharedInstance].autoPlayEnabled) {
        [self startAutoScroll];
    }
}

- (void)checkForWidgetPhotoSelection {
    NSUserDefaults *defaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.lfb.manager.photoclear.shared"];
    NSString *selectedPhotoIdentifier = [defaults objectForKey:@"selectedPhotoIdentifier"];
    
    if (selectedPhotoIdentifier && selectedPhotoIdentifier.length > 0) {
        // 清除标识符，避免重复处理
        [defaults removeObjectForKey:@"selectedPhotoIdentifier"];
        [defaults synchronize];
        
        // 延迟一点时间确保界面已经加载完成
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self scrollToPhotoWithIdentifier:selectedPhotoIdentifier];
        });
        
        NSLog(@"ScrollViewController checking for widget photo selection: %@", selectedPhotoIdentifier);
    }
}

- (void)autoScroll {
    [self showNextImage];
}

- (void)startAutoScroll {
    [self.scrollTimer invalidate];
    if ([Preferences sharedInstance].autoPlayEnabled) {
        NSTimeInterval interval = [Preferences sharedInstance].autoPlayInterval;
        self.scrollTimer = [NSTimer scheduledTimerWithTimeInterval:interval target:self selector:@selector(autoScroll) userInfo:nil repeats:YES];
    }
}

- (void)stopAutoScroll {
    [self.scrollTimer invalidate];
}

//- (void)viewDidAppear:(BOOL)animated {
//    [self.collectionView scrollToItemAtIndexPath:self.currentIndexPath atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
//}

- (void)reloadCollectionView {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        if ([Preferences sharedInstance].lastAlbumLocalIdentifier == nil) {
            self.photoAssetArray = [PhotoTools allPhotos];
        } else {
            PHFetchResult *collections = [PHAssetCollection fetchAssetCollectionsWithLocalIdentifiers:@[[Preferences sharedInstance].lastAlbumLocalIdentifier] options:nil];
            
            PHFetchOptions *fetchOptions = [[PHFetchOptions alloc] init];
            fetchOptions.sortDescriptors = @[[NSSortDescriptor sortDescriptorWithKey:@"creationDate" ascending:NO]];
            //容器类
            PHFetchResult *fetchResult = [PHAsset fetchAssetsInAssetCollection:collections.firstObject options:fetchOptions];
            self.photoAssetArray = fetchResult;
        }
        
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:0 inSection:0];
        if ([Preferences sharedInstance].lastLocalIdentifier) {
            for (NSInteger index = 0; index < self.photoAssetArray.count; index ++) {
                PHAsset *phAsset = [self.photoAssetArray objectAtIndex:index];
                if ([phAsset.localIdentifier isEqualToString:[Preferences sharedInstance].lastLocalIdentifier]) {
                    indexPath = [NSIndexPath indexPathForRow:index inSection:0];
                    break;
                }
            }
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            [self updateDeleteDataAndViews];
            [self.collectionView reloadData];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self.collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
            });
        });
    });
}



- (void)collectionView:(UICollectionView *)collectionView didHighlightItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.albumCollectionView == collectionView) {
        UICollectionViewCell *cell = [collectionView cellForItemAtIndexPath:indexPath];
        [UIView animateWithDuration:0.2 animations:^{
            cell.transform = CGAffineTransformMakeScale(0.8, 0.8);
        }];
    }
}

- (void)collectionView:(UICollectionView *)collectionView didUnhighlightItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.albumCollectionView == collectionView) {
        UICollectionViewCell *cell = [collectionView cellForItemAtIndexPath:indexPath];
        [UIView animateWithDuration:0.2 animations:^{
            cell.transform = CGAffineTransformMakeScale(1, 1);
        }];
    }
}

#pragma mark - <  UICollectionViewDelegate >

//
//- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
//    CGSize cellSize = CGSizeMake(self.cellwidth, collectionView.height);
//    return cellSize;
//}
//
//
//// 两列cell之间的间距
//- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
//    return self.cellMargin;
//}


//- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
//    return self.cellMargin;
//}



- (void)didScrollToPage:(int)page
{
    _currentPage = page;
}

- (void)reloadPhotoeData
{
    [self.collectionView setContentOffset:CGPointMake(_currentPage*CGRectGetWidth(self.collectionView.frame), 0) animated:NO];
    [self.collectionView reloadData];
}

- (BOOL)prefersStatusBarHidden {
    return YES;
}

- (void)updateInfoLabel {
    NSInteger currentIndex = self.currentIndexPath.row;
    PHAsset *phAsset = [self.photoAssetArray objectAtIndex:currentIndex];
    
    NSString *infoStr = [NSString stringWithFormat:@"%ld / %ld", currentIndex + 1, self.photoAssetArray.count];
    if ([Preferences sharedInstance].autoPlayEnabled) {
        infoStr = [NSString stringWithFormat:@"%ld / %ld (自动播放中)", currentIndex + 1, self.photoAssetArray.count];
    }

    self.infoLabel.text = infoStr;
    [self updatedeleteBtn:[self isDelete:phAsset]];
}

- (BOOL)isDelete:(PHAsset *)phAsset {
    if ([self.deletePhArray indexOfObject:phAsset.localIdentifier] != NSNotFound) {
        return YES;
    }
    return NO;
}

- (BOOL)isArchive:(PHAsset *)phAsset {
    if ([self.archivePhArray indexOfObject:phAsset.localIdentifier] != NSNotFound) {
        return YES;
    }
    return NO;
}

- (void)updatedeleteBtn:(BOOL)isDelete {
    if (isDelete) {
        // 当前照片被标记为删除
        [self.deleteBtn setImage:[UIImage systemImageNamed:@"trash.slash"] forState:UIControlStateNormal];
    } else {
        // 默认状态
        [self.deleteBtn setImage:[UIImage systemImageNamed:@"trash"] forState:UIControlStateNormal];
    }
    [self updateDeleteDataAndViews];
}

- (void)updateDeleteDataAndViews {
    self.recycleInfoLabel.text = [NSString stringWithFormat:@"删除(%ld)", self.deletePhArray.count];
    [self.recycleInfoLabel sizeToFit];
    [Preferences sharedInstance].deleteArray = self.deletePhArray;
}

- (void)updateArchiveData {
    [Preferences sharedInstance].archiveArray = self.archivePhArray;
}

- (void)setupToolbarButtons {
    // 设置归档按钮
    [self.archiveBtn setTitle:@"归档" forState:UIControlStateNormal];
    [self.archiveBtn setImage:[UIImage systemImageNamed:@"archivebox"] forState:UIControlStateNormal];
    [self.archiveBtn verticalCenterImageAndTitle:8.0];

    // 设置删除按钮
    [self.deleteBtn setTitle:@"删除" forState:UIControlStateNormal];
    [self.deleteBtn setImage:[UIImage systemImageNamed:@"trash"] forState:UIControlStateNormal];
    [self.deleteBtn verticalCenterImageAndTitle:8.0];

    // 设置下一个按钮
    [self.nextBtn setTitle:@"下一个" forState:UIControlStateNormal];
    [self.nextBtn setImage:[UIImage imageNamed:@"pnext"] forState:UIControlStateNormal];
    [self.nextBtn verticalCenterImageAndTitle:8.0];
}

- (void)setupButtonLiquidEffect:(UIButton *)button {
    // iOS 26 Liquid Glass按钮样式
    button.layer.cornerRadius = 18.0;  // 稍微增加圆角以获得更现代的外观
    button.layer.masksToBounds = YES;

    // 清除背景色，让UIGlassContainerEffect生效
    button.backgroundColor = [UIColor clearColor];

    // iOS 26 Liquid Glass按钮的微妙边框效果
    if (@available(iOS 18.0, *)) {
        button.layer.borderWidth = 0.33;
        button.layer.borderColor = [[UIColor whiteColor] colorWithAlphaComponent:0.15].CGColor;

        // 添加微妙的内阴影效果（通过CALayer实现）
        CALayer *innerShadowLayer = [CALayer layer];
        innerShadowLayer.frame = button.bounds;
        innerShadowLayer.cornerRadius = 18.0;
        innerShadowLayer.shadowColor = [UIColor blackColor].CGColor;
        innerShadowLayer.shadowOffset = CGSizeMake(0, 1);
        innerShadowLayer.shadowOpacity = 0.05;
        innerShadowLayer.shadowRadius = 2;
        [button.layer addSublayer:innerShadowLayer];
    }

    // 添加触摸事件
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpOutside];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchCancel];
}

- (void)buttonTouchDown:(UIButton *)button {
    // iOS 26 Liquid Glass按钮的精致触摸效果
    [UIView animateWithDuration:0.15 delay:0 usingSpringWithDamping:0.8 initialSpringVelocity:0.3 options:UIViewAnimationOptionCurveEaseOut animations:^{
        button.transform = CGAffineTransformMakeScale(0.92, 0.92);
        button.alpha = 0.75;

        // 增强玻璃效果的视觉反馈
        if (@available(iOS 18.0, *)) {
            button.layer.borderColor = [[UIColor whiteColor] colorWithAlphaComponent:0.25].CGColor;
        }
    } completion:nil];

    // 添加更精致的触觉反馈
    if (@available(iOS 13.0, *)) {
        UIImpactFeedbackGenerator *feedbackGenerator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleSoft];
        [feedbackGenerator impactOccurred];
    } else if (@available(iOS 10.0, *)) {
        UIImpactFeedbackGenerator *feedbackGenerator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleLight];
        [feedbackGenerator impactOccurred];
    }
}

- (void)buttonTouchUp:(UIButton *)button {
    // iOS 26 Liquid Glass按钮的流畅回弹效果
    [UIView animateWithDuration:0.3 delay:0 usingSpringWithDamping:0.6 initialSpringVelocity:0.8 options:UIViewAnimationOptionCurveEaseOut animations:^{
        button.transform = CGAffineTransformIdentity;
        button.alpha = 1.0;

        // 恢复玻璃效果的边框
        if (@available(iOS 18.0, *)) {
            button.layer.borderColor = [[UIColor whiteColor] colorWithAlphaComponent:0.15].CGColor;
        }
    } completion:nil];
}

- (void)setupToolbarBlurEffect {
    // 设置系统玻璃效果的基本属性
    self.toolbarBlurView.layer.cornerRadius = 25.0;
    self.toolbarBlurView.clipsToBounds = YES;

    // 使用iOS 26的新Liquid Glass效果
    if (@available(iOS 26.0, *)) {
        // 创建UIGlassEffect - iOS 26的新Liquid Glass API
        UIGlassEffect *glassEffect = [[UIGlassEffect alloc] init];

        // 在动画块中设置效果以获得平滑的过渡
        [UIView animateWithDuration:0.3 animations:^{
            self.toolbarBlurView.effect = glassEffect;
        }];

        // 创建UIGlassContainerEffect来组织按钮
        UIGlassContainerEffect *containerEffect = [[UIGlassContainerEffect alloc] init];

        // 创建vibrancy容器来增强玻璃质感
        self.vibrancyEffectView = [[UIVisualEffectView alloc] initWithEffect:containerEffect];
        self.vibrancyEffectView.frame = self.toolbarView.bounds;
        self.vibrancyEffectView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;

        // 将vibrancy容器添加到工具栏
        [self.toolbarView addSubview:self.vibrancyEffectView];

        // 将按钮移动到vibrancy容器中以获得更好的玻璃效果
        [self moveButtonsToVibrancyView];
    } else {
        // iOS 17及以下的兼容性处理
        UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleSystemMaterial];
        self.toolbarBlurView.effect = blurEffect;
    }

    // 添加系统阴影效果
    [self addSystemShadowEffect];
}

- (void)moveButtonsToVibrancyView {
    if (!self.vibrancyEffectView) return;

    // 将按钮从原来的容器移动到vibrancy容器中
    NSArray *buttons = @[self.archiveBtn, self.deleteBtn, self.nextBtn];

    for (UIButton *button in buttons) {
        // 保存原来的约束信息
        CGRect originalFrame = button.frame;

        // 移除原来的约束
        [button removeFromSuperview];

        // 添加到vibrancy容器的contentView中
        [self.vibrancyEffectView.contentView addSubview:button];

        // 重新设置frame
        button.frame = originalFrame;
        button.translatesAutoresizingMaskIntoConstraints = NO;

        // 重新添加约束
        [self setupButtonConstraintsInVibrancyView:button];

        // 为按钮添加Liquid Glass样式的触摸效果
        [self setupButtonLiquidEffect:button];
    }
}

- (void)setupButtonConstraintsInVibrancyView:(UIButton *)button {
    UIView *containerView = self.vibrancyEffectView.contentView;

    if (button == self.archiveBtn) {
        [NSLayoutConstraint activateConstraints:@[
            [button.leadingAnchor constraintEqualToAnchor:containerView.leadingAnchor constant:25],
            [button.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:10],
            [button.widthAnchor constraintEqualToConstant:80],
            [button.heightAnchor constraintEqualToConstant:60]
        ]];
    } else if (button == self.deleteBtn) {
        [NSLayoutConstraint activateConstraints:@[
            [button.leadingAnchor constraintEqualToAnchor:self.archiveBtn.trailingAnchor constant:5],
            [button.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:10],
            [button.widthAnchor constraintEqualToConstant:80],
            [button.heightAnchor constraintEqualToConstant:60]
        ]];
    } else if (button == self.nextBtn) {
        [NSLayoutConstraint activateConstraints:@[
            [button.leadingAnchor constraintEqualToAnchor:self.deleteBtn.trailingAnchor constant:5],
            [button.topAnchor constraintEqualToAnchor:containerView.topAnchor constant:10],
            [button.widthAnchor constraintEqualToConstant:80],
            [button.heightAnchor constraintEqualToConstant:60],
            [containerView.trailingAnchor constraintEqualToAnchor:button.trailingAnchor constant:25]
        ]];
    }
}

- (void)addSystemShadowEffect {
    // iOS 26 Liquid Glass推荐的阴影参数 - 更加精致和现代
    self.toolbarBlurView.layer.shadowColor = [UIColor blackColor].CGColor;
    self.toolbarBlurView.layer.shadowOffset = CGSizeMake(0, 8);  // 增加垂直偏移以获得更好的深度感
    self.toolbarBlurView.layer.shadowRadius = 20;               // 增加模糊半径以获得更柔和的阴影
    self.toolbarBlurView.layer.shadowOpacity = 0.12;            // 稍微降低不透明度以获得更精致的效果

    // 创建阴影路径以提高性能
    UIBezierPath *shadowPath = [UIBezierPath bezierPathWithRoundedRect:self.toolbarBlurView.bounds cornerRadius:25.0];
    self.toolbarBlurView.layer.shadowPath = shadowPath.CGPath;

    // 确保阴影不被裁剪
    self.toolbarBlurView.layer.masksToBounds = NO;

    // 添加额外的视觉层次感 - Liquid Glass特有的多层阴影效果
    if (@available(iOS 18.0, *)) {
        // 为Liquid Glass添加内阴影效果（通过边框模拟）
        self.toolbarBlurView.layer.borderWidth = 0.5;
        self.toolbarBlurView.layer.borderColor = [[UIColor whiteColor] colorWithAlphaComponent:0.2].CGColor;
    }
}



- (void)updateToolbarBlurEffectLayers {
    if (!self.toolbarBlurView) return;

    // 更新阴影路径
    UIBezierPath *shadowPath = [UIBezierPath bezierPathWithRoundedRect:self.toolbarBlurView.bounds cornerRadius:25.0];
    self.toolbarBlurView.layer.shadowPath = shadowPath.CGPath;

    // 更新vibrancy容器的frame
    if (self.vibrancyEffectView) {
        self.vibrancyEffectView.frame = self.toolbarView.bounds;

        // 更新按钮的内阴影层（如果存在）
        if (@available(iOS 18.0, *)) {
            NSArray *buttons = @[self.archiveBtn, self.deleteBtn, self.nextBtn];
            for (UIButton *button in buttons) {
                // 更新按钮内阴影层的frame
                for (CALayer *sublayer in button.layer.sublayers) {
                    if (sublayer != button.imageView.layer && sublayer != button.titleLabel.layer) {
                        sublayer.frame = button.bounds;
                    }
                }
            }
        }
    }
}

- (void)fetchImageWithIndex:(NSInteger)imageIndex withCompletionHandler:(void (^)(PHAsset * phAsset, UIImage *image))completionHandler {
    if (imageIndex >= self.photoAssetArray.count || imageIndex < 0) {
        if (completionHandler) {
            completionHandler(nil, nil);
        }
        return;
    }
    
    PHAsset *phAsset = [self.photoAssetArray objectAtIndex:imageIndex];
    [[PHImageManager defaultManager] requestImageForAsset:phAsset targetSize:self.imageTargetSize contentMode:PHImageContentModeDefault options:self.requestOptions resultHandler:^(UIImage * _Nullable result, NSDictionary * _Nullable info) {
        if (completionHandler) {
            completionHandler(phAsset, result);
        }
    }];
}

- (PHImageRequestOptions *)requestOptions {
    if (!_requestOptions) {
        _requestOptions = [[PHImageRequestOptions alloc] init];//请求选项设置
        _requestOptions.resizeMode = PHImageRequestOptionsResizeModeExact;//自定义图片大小的加载模式
        _requestOptions.deliveryMode = PHImageRequestOptionsDeliveryModeHighQualityFormat;
        _requestOptions.synchronous = YES;//是否同步加载
    }
    return _requestOptions;
}



- (void)showNextImage {
    if (self.currentIndexPath.row < self.photoAssetArray.count-1) {
        self.collectionView.userInteractionEnabled = NO;
        NSIndexPath *tempIndex = [NSIndexPath indexPathForRow:self.currentIndexPath.row + 1 inSection:0];
        [self.collectionView scrollToItemAtIndexPath:tempIndex atScrollPosition:UICollectionViewScrollPositionNone animated:YES];
        dispatch_async (dispatch_get_main_queue (), ^{
            [self.collectionView reloadItemsAtIndexPaths:@[tempIndex]];
            self.currentIndexPath = tempIndex;
        });
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.collectionView.userInteractionEnabled = YES;
        });
    }
    
//    CGPoint contentOffset = self.collectionView.contentOffset;
//    contentOffset.x += self.collectionView.width;
//    [UIView animateWithDuration:0.24 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
////        [self.collectionView scrollToItemAtIndexPath:[NSIndexPath indexPathForRow:self.currentIndexPath.row - 1 inSection:0] atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
//        [self.collectionView setContentOffset:contentOffset animated:YES];
//    } completion:^(BOOL finished) {
//
//    }];
//
}

- (void)showPreImage {
    if (self.currentIndexPath.row > 0) {
        self.collectionView.userInteractionEnabled = NO;
        NSIndexPath *tempIndex = [NSIndexPath indexPathForRow:self.currentIndexPath.row - 1 inSection:0];
        [self.collectionView scrollToItemAtIndexPath:tempIndex atScrollPosition:UICollectionViewScrollPositionNone animated:YES];
        dispatch_async (dispatch_get_main_queue (), ^{
            [self.collectionView reloadItemsAtIndexPaths:@[tempIndex]];
            self.currentIndexPath = tempIndex;
        });
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.collectionView.userInteractionEnabled = YES;
        });
    }
//    CGPoint contentOffset = self.collectionView.contentOffset;
//    contentOffset.x -= self.collectionView.width;
//    [UIView animateWithDuration:0.24 delay:0 options:UIViewAnimationOptionCurveEaseIn animations:^{
//        [self.collectionView setContentOffset:contentOffset animated:YES];
//    } completion:^(BOOL finished) {
//
//    }];
}

- (IBAction)preBtnClicked:(UIButton *)sender {
    [self showPreImage];
    [self stopAutoScroll];
}

- (IBAction)nextBtnClicked:(UIButton *)sender {
    [self showNextImage];
    [self stopAutoScroll];
}

- (IBAction)deleteBtnClicked:(UIButton *)sender {
    PHAsset *phAsset = [self.photoAssetArray objectAtIndex:self.currentIndexPath.row];

    if ([self isDelete:phAsset]) {
        [self.deletePhArray removeObject:phAsset.localIdentifier];
    } else {
        [self.deletePhArray addObject:phAsset.localIdentifier];
        [self showNextImage];
    }

    [self updatedeleteBtn:[self isDelete:phAsset]];
    [self stopAutoScroll];
//    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//        [self startAutoScroll];
//    });
}

- (IBAction)archiveBtnClicked:(UIButton *)sender {
    PHAsset *phAsset = [self.photoAssetArray objectAtIndex:self.currentIndexPath.row];

    if ([self isArchive:phAsset]) {
        [self.archivePhArray removeObject:phAsset.localIdentifier];
    } else {
        [self.archivePhArray addObject:phAsset.localIdentifier];
        [self showNextImage];
    }

    [self updateArchiveData];
    [self stopAutoScroll];
}

//- (IBAction)nextBtnClicked:(UIButton *)sender {
//    [self showNextImage];
//    [self stopAutoScroll];
//}

- (IBAction)recycleBtnClicked:(id)sender {
    if (self.deletePhArray.count > 0) {
        
        // 如果有待删除的照片，跳转到删除列表页面
        DeleteListViewController *deleteListVC = [[DeleteListViewController alloc] init];
        deleteListVC.deletePhotoIdentifiers = [self.deletePhArray mutableCopy];
        deleteListVC.delegate = self;
        deleteListVC.modalPresentationStyle = UIModalPresentationFullScreen;
        
        [self presentViewController:deleteListVC animated:YES completion:nil];
        
//        [[PHPhotoLibrary sharedPhotoLibrary] performChanges:^{
//                // 查找set
//            PHFetchResult<PHAsset *> *result = [PHAsset fetchAssetsWithLocalIdentifiers:self.deletePhArray options:nil];
//            [PHAssetChangeRequest deleteAssets:result];
//        } completionHandler:^(BOOL success, NSError * _Nullable error) {
//            
//            if (success) {
//                NSLog(@"删除成功！");
//                [self.deletePhArray removeAllObjects];
//                [Preferences sharedInstance].deleteArray = nil;
//                [self reloadCollectionView];
//            } else {
//                NSLog(@"删除失败：%@", error);
//            }
//        }];
    }
}

- (IBAction)selectPhotoAlbum:(UIButton *)sender {
    UIStoryboard *storyBoard = [UIStoryboard storyboardWithName:@"Main" bundle:nil];
    UINavigationController *albumselectnavigation = [storyBoard instantiateViewControllerWithIdentifier:@"albumselectnavigation"];
    AlbumSelectViewController *albumSelectViewController = albumselectnavigation.viewControllers.firstObject;
    albumSelectViewController.delegate = self;
    
    [self presentViewController:albumselectnavigation animated:YES completion:nil];
}

- (IBAction)addAlbum:(UIButton *)sender {
    UIAlertController *alertVC = [UIAlertController alertControllerWithTitle:@"新增相簿" message:@"新增的相簿会在系统相册同步创建" preferredStyle:UIAlertControllerStyleAlert];

    [alertVC addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
       textField.placeholder = @"请输入相簿";
    }];
    UIAlertAction *cancel = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleCancel handler:nil];
    [alertVC addAction:cancel];
    
    UIAlertAction *ok = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
       NSLog(@"登录 = %@",[alertVC.textFields firstObject].text);
        //创建新的相簿
        __block NSString *assetCollectionLocalIdentifier = nil;
        __block NSString *photoAssetCollectionName = [alertVC.textFields firstObject].text;
        NSError *error = nil;
        //同步方法
        [[PHPhotoLibrary sharedPhotoLibrary]performChangesAndWait:^{
            // 创建相簿的请求
            assetCollectionLocalIdentifier = [PHAssetCollectionChangeRequest creationRequestForAssetCollectionWithTitle:photoAssetCollectionName].placeholderForCreatedAssetCollection.localIdentifier;
        } error:&error];

        if (!error) {
            PHFetchResult *smartAlbums = [PHAssetCollection fetchAssetCollectionsWithType:PHAssetCollectionTypeAlbum subtype:PHAssetCollectionSubtypeAny options:nil];
            self.smartAlbumsArray = smartAlbums;
            [self.albumCollectionView reloadData];
        }
        
    }];
    [alertVC addAction:ok];
    [self presentViewController:alertVC animated:YES completion:nil];
}

- (void)singleTapd:(UITapGestureRecognizer *)gestureRecognizer {
    CGPoint point = [gestureRecognizer locationInView:self.view];
    NSLog(@"point = (%lf, %lf)",point.x, point.y);
    
    // 根据设置决定滑动方向
    NSInteger swipeDirection = [Preferences sharedInstance].swipeDirection;
    
    if (swipeDirection == 0) { // 上下滑动切换
        if (point.y < self.view.height/4) {
            [self showPreImage];
            [self stopAutoScroll];
        } else if (point.y > self.view.height/4 * 3) {
            [self showNextImage];
            [self stopAutoScroll];
        } else {
            [self toggleAutoPlay];
        }
    } else { // 左右滑动切换
        if (point.x < self.view.width/4) {
            [self showPreImage];
            [self stopAutoScroll];
        } else if (point.x > self.view.width/4 * 3) {
            [self showNextImage];
            [self stopAutoScroll];
        } else {
            [self toggleAutoPlay];
        }
    }
}

- (void)toggleAutoPlay {
    if (self.scrollTimer.isValid) {
        [self stopAutoScroll];
    } else {
        [self startAutoScroll];
    }
}

- (void)handleLongPress:(UILongPressGestureRecognizer *)gestureRecognizer {
    if (gestureRecognizer.state == UIGestureRecognizerStateBegan) {
        // 长按开始时标记/取消标记当前照片
        PHAsset *phAsset = [self.photoAssetArray objectAtIndex:self.currentIndexPath.row];
        
        if ([self isDelete:phAsset]) {
            [self.deletePhArray removeObject:phAsset.localIdentifier];
        } else {
            [self.deletePhArray addObject:phAsset.localIdentifier];
        }
        
        [self updatedeleteBtn:[self isDelete:phAsset]];
        [self stopAutoScroll];
        
        // 提供触觉反馈
        if (@available(iOS 10.0, *)) {
            UIImpactFeedbackGenerator *feedbackGenerator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleMedium];
            [feedbackGenerator impactOccurred];
        }
    }
}

- (void)AlbumSelectViewControllerDidSelect:(PHAssetCollection *)collection andPhotoAsset:(PHFetchResult<PHAsset *> *)photoAssetArray andIndex:(NSInteger)index {
    self.photoAssetArray = photoAssetArray;
    self.currentIndexPath = [NSIndexPath indexPathForRow:index inSection:0];
    [self.deletePhArray removeAllObjects];
    [Preferences sharedInstance].deleteArray = self.deletePhArray;
    [Preferences sharedInstance].lastAlbumLocalIdentifier = collection.localIdentifier;
    
    [self.collectionView reloadData];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.collectionView scrollToItemAtIndexPath:[NSIndexPath indexPathForRow:index inSection:0] atScrollPosition:UICollectionViewScrollPositionNone animated:NO];
    });
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    [self updateToolbarBlurEffectLayers];
}

- (void)viewWillTransitionToSize:(CGSize)size withTransitionCoordinator:(id<UIViewControllerTransitionCoordinator>)coordinator {
    [super viewWillTransitionToSize:size withTransitionCoordinator:coordinator];
    self.imageTargetSize = [self getTargetSize];

    FQ_CollectionViewFlowLayout * flowLayout = [[FQ_CollectionViewFlowLayout alloc]init];
    flowLayout.minimumLineSpacing = BUBBLE_PADDING;
    self.collectionView.collectionViewLayout = flowLayout;
    [self.collectionView reloadData];
    [self.collectionView setNeedsLayout];
}

- (CGSize)getTargetSize {
    CGFloat maxWidth = self.view.width;
    if (self.view.height > self.view.width) {
        maxWidth = self.view.height;
    }
    return CGSizeMake(maxWidth*2, maxWidth*2);
}

- (void)setCompleteButton:(UIButton *)button iconString:(NSString *)iconString image:(UIImage *)image {
    CGSize strSize = [iconString sizeWithAttributes:@{NSFontAttributeName:button.titleLabel.font}];
    CGFloat imageLeftInset = floor((button.width-image.size.width)/2);
    CGFloat imageRightInset = floor(button.width-(imageLeftInset+image.size.width+strSize.width));
    button.imageEdgeInsets = UIEdgeInsetsMake(0, imageLeftInset, button.height-image.size.height, imageRightInset);
    CGFloat titleRightInset = floor((button.width-strSize.width)/2);
    CGFloat titleLeftInset = floor(button.width-(image.size.width+strSize.width+titleRightInset));
    button.titleEdgeInsets = UIEdgeInsetsMake(button.height-strSize.height, titleLeftInset, 0, titleRightInset);
}

#pragma mark - Widget Photo Selection

- (void)handleShowPhotoFromWidget:(NSNotification *)notification {
    NSString *photoIdentifier = notification.userInfo[@"photoIdentifier"];
    if (photoIdentifier && photoIdentifier.length > 0) {
        [self scrollToPhotoWithIdentifier:photoIdentifier];
    }
}

- (void)scrollToPhotoWithIdentifier:(NSString *)photoIdentifier {
    if (!self.photoAssetArray || self.photoAssetArray.count == 0) {
        // 如果还没有加载照片数据，先加载
        [self reloadCollectionView];
        
        // 延迟执行定位
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self scrollToPhotoWithIdentifier:photoIdentifier];
        });
        return;
    }
    
    // 在当前照片数组中查找匹配的照片
    NSInteger targetIndex = -1;
    for (NSInteger index = 0; index < self.photoAssetArray.count; index++) {
        PHAsset *phAsset = [self.photoAssetArray objectAtIndex:index];
        
        // 提取UUID部分进行比较
        NSString *assetUUID = [self extractUUIDFromLocalIdentifier:phAsset.localIdentifier];
        NSString *targetUUID = [self extractUUIDFromLocalIdentifier:photoIdentifier];
        
        if ([assetUUID isEqualToString:targetUUID]) {
            targetIndex = index;
            break;
        }
    }
    
    if (targetIndex >= 0) {
        // 找到了匹配的照片，滚动到该位置
        NSIndexPath *targetIndexPath = [NSIndexPath indexPathForRow:targetIndex inSection:0];
        self.currentIndexPath = targetIndexPath;
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.collectionView scrollToItemAtIndexPath:targetIndexPath 
                                        atScrollPosition:UICollectionViewScrollPositionNone 
                                                animated:NO];
            [self updateInfoLabel];
        });
        
        NSLog(@"Found and scrolled to photo at index: %ld", (long)targetIndex);
    } else {
        NSLog(@"Photo with identifier %@ not found in current album", photoIdentifier);
    }
}

- (NSString *)extractUUIDFromLocalIdentifier:(NSString *)localIdentifier {
    // PHAsset的localIdentifier格式通常是: "6218C9ED-52CD-432D-A2E7-1C0D15C2EF0A/L0/001"
    // 我们只需要UUID部分
    NSArray *components = [localIdentifier componentsSeparatedByString:@"/"];
    if (components.count > 0) {
        return components[0];
    }
    return localIdentifier;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - SettingsViewControllerDelegate

- (void)settingsDidChange {
    // 重新启动自动播放（如果启用）
    [self stopAutoScroll];
    if ([Preferences sharedInstance].autoPlayEnabled) {
        [self startAutoScroll];
    }
    
    // 重新设置滑动手势
    [self setupSwipeGestures];
    
    // 重新配置collectionView布局
    [self setupCollectionViewLayout];
}

- (void)setupSwipeGestures {
    // 移除现有的滑动手势
    for (UIGestureRecognizer *gesture in self.view.gestureRecognizers) {
        if ([gesture isKindOfClass:[UISwipeGestureRecognizer class]]) {
            [self.view removeGestureRecognizer:gesture];
        }
    }
    
    NSInteger swipeDirection = [Preferences sharedInstance].swipeDirection;
    
    if (swipeDirection == 0) { // 上下滑动切换
        // 向上滑动 - 下一张
        UISwipeGestureRecognizer *swipeUp = [[UISwipeGestureRecognizer alloc] initWithTarget:self action:@selector(handleSwipeUp:)];
        swipeUp.direction = UISwipeGestureRecognizerDirectionUp;
        [self.view addGestureRecognizer:swipeUp];
        
        // 向下滑动 - 上一张
        UISwipeGestureRecognizer *swipeDown = [[UISwipeGestureRecognizer alloc] initWithTarget:self action:@selector(handleSwipeDown:)];
        swipeDown.direction = UISwipeGestureRecognizerDirectionDown;
        [self.view addGestureRecognizer:swipeDown];
    } else { // 左右滑动切换
        // 向左滑动 - 下一张
        UISwipeGestureRecognizer *swipeLeft = [[UISwipeGestureRecognizer alloc] initWithTarget:self action:@selector(handleSwipeLeft:)];
        swipeLeft.direction = UISwipeGestureRecognizerDirectionLeft;
        [self.view addGestureRecognizer:swipeLeft];
        
        // 向右滑动 - 上一张
        UISwipeGestureRecognizer *swipeRight = [[UISwipeGestureRecognizer alloc] initWithTarget:self action:@selector(handleSwipeRight:)];
        swipeRight.direction = UISwipeGestureRecognizerDirectionRight;
        [self.view addGestureRecognizer:swipeRight];
    }
}

#pragma mark - Swipe Gesture Handlers

- (void)handleSwipeUp:(UISwipeGestureRecognizer *)gesture {
    [self showNextImage];
    [self stopAutoScroll];
}

- (void)handleSwipeDown:(UISwipeGestureRecognizer *)gesture {
    [self showPreImage];
    [self stopAutoScroll];
}

- (void)handleSwipeLeft:(UISwipeGestureRecognizer *)gesture {
    [self showNextImage];
    [self stopAutoScroll];
}

- (void)handleSwipeRight:(UISwipeGestureRecognizer *)gesture {
    [self showPreImage];
    [self stopAutoScroll];
}


- (IBAction)closeBtnClicked:(UIButton *)sender {
    [self.navigationController popViewControllerAnimated:YES];
}

- (IBAction)setBtnClicked:(UIButton *)sender {
    PhotoSettingsViewController *settingsVC = [[PhotoSettingsViewController alloc] init];
    settingsVC.delegate = self;
    settingsVC.modalPresentationStyle = UIModalPresentationOverCurrentContext;
    settingsVC.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    
    [self presentViewController:settingsVC animated:NO completion:nil];
}


#pragma mark - UICollectionViewDataSource
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    if (self.albumCollectionView == collectionView) {
        return self.smartAlbumsArray.count;
    }
    return self.photoAssetArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
//    if (collectionView == self.albumCollectionView) {
//        ScrollActionCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"ScrollActionCollectionViewCell" forIndexPath:indexPath];
//        PHAssetCollection *smartAlbum = [self.smartAlbumsArray objectAtIndex:indexPath.row];
//        [cell updateTitle:smartAlbum.localizedTitle];
//        return cell;
//    } else {
        FQ_CollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"FQ_CollectionViewCellID" forIndexPath:indexPath];
        cell.delegate = self;
        [cell updateImage:nil];
        
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            [self fetchImageWithIndex:indexPath.row withCompletionHandler:^(PHAsset *phAsset, UIImage *image) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [cell updateImage:image];
                });
            }];
        });
        [self updateInfoLabel];
        return cell;
//    }
}

#pragma mark - UICollectionViewDelegateFlowLayout
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath
{
    return CGSizeMake(self.view.bounds.size.width+20, self.view.bounds.size.height);
}

#pragma mark - UICollectionViewDelegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.albumCollectionView == collectionView) {
        [collectionView deselectItemAtIndexPath:indexPath animated:YES];
        PHAssetCollection *smartAlbum = [self.smartAlbumsArray objectAtIndex:indexPath.row];
        
        // 将刚才添加到【相机胶卷】的图片，引用（添加）到【自定义相册】
        PHAsset *phAsset = [self.photoAssetArray objectAtIndex:self.currentIndexPath.row];
           NSError *error = nil;
            [[PHPhotoLibrary sharedPhotoLibrary] performChangesAndWait:^{
                   PHAssetCollectionChangeRequest *request = [PHAssetCollectionChangeRequest changeRequestForAssetCollection:smartAlbum];
                [request addAssets:@[phAsset]];
            } error:&error];
           // 保存结果
           if (error) {
               NSLog(@"保存失败！");
           } else {
               NSLog(@"保存成功！");
           }
        
        
        UICollectionViewCell *cell = [collectionView cellForItemAtIndexPath:indexPath];
        [UIView animateWithDuration:0.1 animations:^{
            cell.transform = CGAffineTransformMakeScale(0.8, 0.8);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.1 animations:^{
                cell.transform = CGAffineTransformMakeScale(1, 1);
            }];
        }];
        
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    NSLog(@"scrollViewDidEndDecelerating");
    if (scrollView == self.collectionView) {
        
        // 将collectionView在控制器view的中心点转化成collectionView上的坐标
        CGPoint pInView = [self.view convertPoint:self.collectionView.center toView:self.collectionView];
        // 获取这一点的indexPath
        NSIndexPath *indexPathNow = [self.collectionView indexPathForItemAtPoint:pInView];
        // 赋值给记录当前坐标的变量
        self.currentIndexPath = indexPathNow;
        
        NSInteger currentIndex = self.currentIndexPath.row;
        PHAsset *phAsset = [self.photoAssetArray objectAtIndex:currentIndex];
        [Preferences sharedInstance].lastLocalIdentifier = phAsset.localIdentifier;
        [self updateInfoLabel];
        
        CGFloat offsetX = scrollView.contentOffset.x;
        float itemWidth = CGRectGetWidth(self.collectionView.frame);
        if (offsetX >= 0){
            int page = offsetX / itemWidth;
            [self didScrollToPage:page];
        }
        
    }
}

- (UICollectionView *)collectionView{
    if (!_collectionView) {
        [self setupCollectionViewLayout];
    }
    return _collectionView;
}

- (void)setupCollectionViewLayout {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.minimumLineSpacing = 0.0;
    layout.minimumInteritemSpacing = 0.0;
    
    // 根据设置决定滚动方向
    NSInteger swipeDirection = [Preferences sharedInstance].swipeDirection;
    if (swipeDirection == 1) {
        // 竖向滑动
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    } else {
        // 默认左右滑动
        layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    }
    
    if (_collectionView) {
        // 如果collectionView已存在，更新布局
        _collectionView.collectionViewLayout = layout;
    } else {
        // 创建新的collectionView
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectMake(-10, 0, self.view.bounds.size.width+20, self.view.bounds.size.height) collectionViewLayout:layout];
        _collectionView.backgroundColor = [UIColor clearColor];
        [_collectionView registerClass:[FQ_CollectionViewCell class] forCellWithReuseIdentifier:@"FQ_CollectionViewCellID"];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.pagingEnabled = YES;
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.showsVerticalScrollIndicator = NO;
        [self.view insertSubview:_collectionView atIndex:0];
    }
}

#pragma mark - DeleteListViewControllerDelegate

- (void)deleteListDidConfirmDeletion:(NSArray<NSString *> *)deletedIdentifiers {
    // 清空删除数组
    [self.deletePhArray removeAllObjects];
    [Preferences sharedInstance].deleteArray = nil;
    
    // 清空操作过的照片记录，因为用户已经完成了删除操作
    [PhotoTools clearOperatedPhotos];
    
    // 重新加载数据
    [self reloadCollectionView];
}

- (void)deleteListDidRemovePhoto:(NSString *)photoIdentifier {
    // 从本地删除数组中移除照片标识符
    [self.deletePhArray removeObject:photoIdentifier];
    
    // 同步更新 Preferences
    [Preferences sharedInstance].deleteArray = [self.deletePhArray copy];
    
    // 更新删除按钮状态
    if (self.currentIndexPath && self.currentIndexPath.row < self.photoAssetArray.count) {
        PHAsset *currentAsset = [self.photoAssetArray objectAtIndex:self.currentIndexPath.row];
        [self updatedeleteBtn:[self isDelete:currentAsset]];
    }
}

@end
